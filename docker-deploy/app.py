"""
Flask Web应用主文件
"""
from flask import Flask, render_template, request, jsonify, send_file, session
import os
import json
import uuid
import threading
import time
from datetime import datetime
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    print("警告: pandas未安装，Excel导出功能将受限")

from database import DatabaseManager
from web_spider import WebSpider

app = Flask(__name__)
app.secret_key = 'cninfo_spider_secret_key_2024'

# 初始化数据库和爬虫
# 自动检测环境并设置合适的路径
def get_database_path():
    """自动检测环境并返回合适的数据库路径"""
    # 检查是否在Docker容器中
    if os.path.exists('/.dockerenv'):
        # Docker环境
        db_path = 'data/database/cninfo_reports.db'
        print("🐳 检测到Docker环境")
    else:
        # 本地环境，检查多个可能的路径
        possible_paths = [
            'cninfo_reports.db',  # 当前目录
            'database/cninfo_reports.db',  # database子目录
            '../cninfo_reports.db',  # 上级目录（cninfo_process目录）
            'data/database/cninfo_reports.db',  # Docker挂载路径
        ]

        db_path = None
        print(f"🔍 正在检查数据库路径...")
        for path in possible_paths:
            print(f"  检查: {path} -> {'存在' if os.path.exists(path) else '不存在'}")
            if os.path.exists(path):
                db_path = path
                print(f"💻 本地环境，找到数据库: {path}")
                break

        if not db_path:
            # 如果都没找到，使用默认路径
            db_path = 'cninfo_reports.db'
            print(f"💻 本地环境，使用默认路径: {db_path}")

    return db_path

# 确保必要目录存在
os.makedirs('data/database', exist_ok=True)
os.makedirs('txt', exist_ok=True)
os.makedirs('pdf', exist_ok=True)

# 获取数据库路径并初始化
db_path = get_database_path()
db_manager = DatabaseManager(db_path=db_path)
spider = WebSpider(db_manager)

# 全局任务状态存储
task_status = {}

def auto_import_txt_files():
    """容器启动时自动导入TXT文件"""
    try:
        txt_dir = 'txt'
        if not os.path.exists(txt_dir):
            print("📁 TXT目录不存在，跳过自动导入")
            return

        # 检查数据库中是否已有数据
        existing_reports = db_manager.get_all_reports()
        if existing_reports:
            print(f"📊 数据库中已有 {len(existing_reports)} 条年报记录，跳过自动导入")
            return

        # 获取TXT文件列表
        txt_files = [f for f in os.listdir(txt_dir) if f.endswith('.txt')]
        if not txt_files:
            print("📁 TXT目录为空，跳过自动导入")
            return

        print(f"🚀 检测到 {len(txt_files)} 个TXT文件，开始自动导入...")

        imported = 0
        skipped = 0
        errors = 0

        for txt_file in txt_files:
            try:
                file_path = os.path.join(txt_dir, txt_file)

                # 解析文件名获取信息
                parts = txt_file.replace('.txt', '').split('_')
                if len(parts) >= 3:
                    stock_code = parts[0]
                    company_name = parts[1]
                    report_title = '_'.join(parts[2:])
                else:
                    stock_code = 'Unknown'
                    company_name = 'Unknown'
                    report_title = txt_file.replace('.txt', '')

                # 检查是否已存在
                if db_manager.report_exists(stock_code, txt_file):
                    skipped += 1
                    continue

                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                if content.strip():
                    # 添加到数据库
                    db_manager.add_report(
                        stock_code=stock_code,
                        company_name=company_name,
                        report_title=report_title,
                        file_name=txt_file,
                        file_path=file_path,
                        txt_content=content
                    )
                    imported += 1

                    # 添加公司信息（如果不存在）
                    try:
                        db_manager.add_company(stock_code, company_name, '')
                    except:
                        pass  # 公司可能已存在
                else:
                    errors += 1

            except Exception as e:
                print(f"❌ 导入文件失败 {txt_file}: {e}")
                errors += 1

        print(f"✅ 自动导入完成: 新增 {imported} 个，跳过 {skipped} 个，失败 {errors} 个")

    except Exception as e:
        print(f"❌ 自动导入过程出错: {e}")

# 容器启动时自动导入TXT文件
print("🐳 Docker容器启动中...")
auto_import_txt_files()


@app.route('/')
def index():
    """主页"""
    return render_template('index.html')


@app.route('/api/companies')
def get_companies():
    """获取公司列表"""
    companies = db_manager.get_companies()
    return jsonify({
        'success': True,
        'data': companies
    })


@app.route('/api/import_txt', methods=['POST'])
def import_txt_files():
    """导入txt文件到数据库"""
    try:
        txt_dir = request.json.get('txt_dir', 'txt')
        result = db_manager.import_txt_files(txt_dir)

        # 构建详细的消息
        message_parts = []
        if result['imported'] > 0:
            message_parts.append(f"成功导入 {result['imported']} 个文件")
        if result['skipped'] > 0:
            message_parts.append(f"跳过重复 {result['skipped']} 个文件")
        if result['errors'] > 0:
            message_parts.append(f"失败 {result['errors']} 个文件")

        message = "，".join(message_parts) if message_parts else "没有文件需要处理"

        return jsonify({
            'success': True,
            'message': f'导入完成：{message}',
            'imported': result['imported'],
            'skipped': result['skipped'],
            'errors': result['errors'],
            'total': result['imported'] + result['skipped'] + result['errors']
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导入失败: {str(e)}'
        })


@app.route('/api/search_reports', methods=['POST'])
def search_reports():
    """搜索年报"""
    try:
        data = request.json
        keyword = data.get('keyword')
        stock_codes = data.get('stock_codes', [])
        year = data.get('year')
        
        reports = db_manager.search_reports(keyword, stock_codes, year)
        return jsonify({
            'success': True,
            'data': reports
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'搜索失败: {str(e)}'
        })


@app.route('/api/start_analysis', methods=['POST'])
def start_analysis():
    """开始分析任务"""
    try:
        data = request.json
        stock_codes = [code.strip() for code in data.get('stock_codes', '').split('\n') if code.strip()]
        keywords = [kw.strip() for kw in data.get('keywords', '').split('\n') if kw.strip()]
        search_keyword = data.get('search_keyword', '年度报告')
        start_date = data.get('start_date', '2024-01-01')
        end_date = data.get('end_date', '2025-12-31')
        use_online = data.get('use_online', True)
        related_parties = [rp.strip() for rp in data.get('related_parties', '').split('\n') if rp.strip()]

        
        if not stock_codes:
            return jsonify({
                'success': False,
                'message': '请输入股票代码'
            })
        
        if not keywords:
            return jsonify({
                'success': False,
                'message': '请输入关键词'
            })
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        task_name = f"关键词分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 创建任务记录
        db_manager.create_analysis_task(task_id, task_name, keywords, stock_codes)
        
        # 初始化任务状态
        task_status[task_id] = {
            'status': 'running',
            'progress': 0,
            'message': '任务开始...',
            'current_step': 0,
            'total_steps': len(stock_codes),
            'results': None,
            'start_time': datetime.now().isoformat()
        }
        
        # 启动后台任务
        def run_analysis():
            def progress_callback(current, total, message):
                progress = int((current / total) * 100)
                task_status[task_id].update({
                    'progress': progress,
                    'message': message,
                    'current_step': current,
                    'total_steps': total
                })
                db_manager.update_task_progress(task_id, progress)
            
            try:
                results = spider.crawl_and_analyze(
                    stock_codes=stock_codes,
                    keywords=keywords,
                    search_keyword=search_keyword,
                    start_date=start_date,
                    end_date=end_date,
                    use_online=use_online,
                    task_id=task_id,
                    related_parties=related_parties,
                    innovation_keywords=keywords,
                    progress_callback=progress_callback
                )
                
                task_status[task_id].update({
                    'status': 'completed',
                    'progress': 100,
                    'message': '任务完成',
                    'results': results,
                    'end_time': datetime.now().isoformat()
                })
                
                db_manager.update_task_progress(task_id, 100, 'completed')
                
            except Exception as e:
                task_status[task_id].update({
                    'status': 'error',
                    'message': f'任务失败: {str(e)}',
                    'end_time': datetime.now().isoformat()
                })
                db_manager.update_task_progress(task_id, 0, 'error')
        
        # 启动线程
        thread = threading.Thread(target=run_analysis)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '任务已启动'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'启动任务失败: {str(e)}'
        })


@app.route('/api/task_status/<task_id>')
def get_task_status(task_id):
    """获取任务状态"""
    if task_id in task_status:
        return jsonify({
            'success': True,
            'data': task_status[task_id]
        })
    else:
        # 从数据库获取任务状态
        task_info = db_manager.get_task_status(task_id)
        if task_info:
            return jsonify({
                'success': True,
                'data': {
                    'status': task_info['status'],
                    'progress': task_info['progress'],
                    'message': f"任务状态: {task_info['status']}",
                    'task_name': task_info['task_name']
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': '任务不存在'
            })


@app.route('/api/stop_task/<task_id>', methods=['POST'])
def stop_task(task_id):
    """停止任务"""
    try:
        spider.stop_crawling()
        if task_id in task_status:
            task_status[task_id].update({
                'status': 'stopped',
                'message': '任务已停止',
                'end_time': datetime.now().isoformat()
            })
        
        db_manager.update_task_progress(task_id, 0, 'stopped')
        
        return jsonify({
            'success': True,
            'message': '任务已停止'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'停止任务失败: {str(e)}'
        })


@app.route('/api/analysis_results/<task_id>')
def get_analysis_results(task_id):
    """获取分析结果"""
    try:
        if task_id in task_status and task_status[task_id].get('results'):
            results = task_status[task_id]['results']
            # 提取analysis_results部分
            if 'analysis_results' in results:
                return jsonify({
                    'success': True,
                    'data': results['analysis_results']
                })
            else:
                return jsonify({
                    'success': True,
                    'data': results
                })
        else:
            # 从数据库获取结果
            analysis_results = db_manager.get_keyword_analysis(task_id)
            return jsonify({
                'success': True,
                'data': analysis_results
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取结果失败: {str(e)}'
        })


@app.route('/api/export_results/<task_id>')
def export_results(task_id):
    """导出分析结果为Excel"""
    try:
        analysis_results = db_manager.get_keyword_analysis(task_id)
        
        if not analysis_results:
            return jsonify({
                'success': False,
                'message': '没有找到分析结果'
            })
        
        if not HAS_PANDAS:
            return jsonify({
                'success': False,
                'message': 'pandas未安装，无法导出Excel文件'
            })

        # 按照新格式组织数据：公司名称作为行，关键词作为列
        # 首先获取所有唯一的关键词
        keywords = sorted(list(set(result['keyword'] for result in analysis_results)))

        # 按公司分组数据
        company_data = {}
        for result in analysis_results:
            company_name = result['company_name']  # 只使用公司名称作为key
            if company_name not in company_data:
                company_data[company_name] = {}
                # 初始化所有关键词为0
                for keyword in keywords:
                    company_data[company_name][keyword] = 0

            # 设置该关键词的出现次数
            company_data[company_name][result['keyword']] = result['count']

        # 转换为DataFrame格式
        df_data = []
        for company_name, data in company_data.items():
            row = {
                '公司名称': company_name  # 只显示公司名称
            }
            # 添加每个关键词的出现次数
            for keyword in keywords:
                row[keyword] = data[keyword]
            df_data.append(row)

        # 创建DataFrame，列顺序为：公司名称 + 关键词列
        columns = ['公司名称'] + keywords
        df = pd.DataFrame(df_data, columns=columns)

        # 保存Excel文件
        export_dir = 'exports'
        if not os.path.exists(export_dir):
            os.makedirs(export_dir)

        filename = f"analysis_results_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filepath = os.path.join(export_dir, filename)

        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='分析结果', index=False)

            # 获取工作表对象进行格式化
            worksheet = writer.sheets['分析结果']

            # 设置列宽
            worksheet.column_dimensions['A'].width = 30  # 公司名称列更宽

            # 设置关键词列宽度
            for i, keyword in enumerate(keywords, start=2):  # 从B列开始
                col_letter = chr(ord('A') + i - 1)  # 转换为Excel列字母
                if i <= 26:  # 只处理A-Z列
                    worksheet.column_dimensions[col_letter].width = 12
        
        return send_file(filepath, as_attachment=True, download_name=filename)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        })


@app.route('/api/keyword_analysis', methods=['POST'])
def keyword_analysis_only():
    """仅进行关键词分析（使用本地数据）"""
    try:
        print("🔍 开始关键词分析...")
        data = request.json
        print(f"📥 接收到数据: {data}")

        stock_codes = [code.strip() for code in data.get('stock_codes', '').split('\n') if code.strip()]
        keywords = [kw.strip() for kw in data.get('keywords', '').split('\n') if kw.strip()]
        related_parties = [rp.strip() for rp in data.get('related_parties', '').split('\n') if rp.strip()]

        print(f"📈 股票代码: {stock_codes}")
        print(f"🔤 关键词: {keywords}")
        print(f"👥 关联方: {related_parties}")
        print(f"💡 协同创新关键词: {keywords} (使用统计关键词)")

        if not stock_codes:
            print("❌ 没有股票代码")
            return jsonify({
                'success': False,
                'message': '请输入股票代码'
            })

        if not keywords:
            print("❌ 没有关键词")
            return jsonify({
                'success': False,
                'message': '请输入关键词'
            })

        # 获取本地年报数据
        print("📊 查询本地年报数据...")
        reports = db_manager.get_reports_by_stock_codes(stock_codes)
        print(f"📄 找到 {len(reports)} 个年报")

        if not reports:
            print("❌ 没有找到年报数据")
            return jsonify({
                'success': False,
                'message': '没有找到相关的年报数据，请先导入txt文件或在线下载'
            })

        # 生成分析ID
        analysis_id = f"keyword_analysis_{int(time.time())}"

        # 创建与"开始分析"一致的结果格式
        analysis_results = {}
        related_party_analysis = {}

        print("🔍 开始分析关键词...")
        # 分析每个年报
        for i, report in enumerate(reports, 1):
            print(f"[{i}/{len(reports)}] 分析: {report.get('file_name', 'Unknown')}")
            if report.get('txt_content'):
                keyword_stats = spider.analyze_keywords(report['txt_content'], keywords)
                print(f"  📊 关键词统计: {keyword_stats}")

                # 保存分析结果到数据库
                db_manager.save_keyword_analysis(
                    analysis_id, report['stock_code'], report['id'], keyword_stats
                )

                # 构建与"开始分析"一致的数据结构
                stock_code = report['stock_code']
                file_name = report.get('file_name', '')

                if stock_code not in analysis_results:
                    analysis_results[stock_code] = {}

                analysis_results[stock_code][file_name] = keyword_stats

                # 关联方分析
                if related_parties and keywords:
                    related_analysis = spider.analyze_related_parties(
                        report['txt_content'], related_parties, keywords
                    )
                    if related_analysis:
                        if stock_code not in related_party_analysis:
                            related_party_analysis[stock_code] = {}
                        related_party_analysis[stock_code][file_name] = related_analysis

        print("✅ 关键词分析完成")

        result = {
            'success': True,
            'analysis_id': analysis_id,
            'data': analysis_results,
            'related_party_analysis': related_party_analysis,
            'message': f'分析完成，共分析 {len(reports)} 个年报文件'
        }

        # 计算结果总数
        total_results = sum(len(files) for files in analysis_results.values())
        print(f"📤 准备返回 {total_results} 条结果")

        try:
            print("🔄 开始JSON序列化...")
            response = jsonify(result)
            print("✅ JSON序列化成功，准备返回响应")
            return response
        except Exception as json_error:
            print(f"❌ JSON序列化失败: {json_error}")
            return jsonify({
                'success': True,
                'analysis_id': analysis_id,
                'data': [],
                'message': f'分析完成但返回数据过大，请查看数据库结果'
            })

    except Exception as e:
        print(f"❌ 关键词分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'分析失败: {str(e)}'
        })


@app.route('/api/keyword_context/<analysis_id>/<keyword>', methods=['GET'])
def get_keyword_context(analysis_id, keyword):
    """获取关键词上下文片段"""
    try:
        print(f"🔍 获取上下文: analysis_id={analysis_id}, keyword={keyword}")

        # 获取上下文长度参数
        context_length = request.args.get('context_length', '100')
        try:
            context_length = int(context_length)
        except:
            context_length = 100

        print(f"📏 上下文长度设置: {context_length} 字符")

        # 获取过滤参数
        stock_code_filter = request.args.get('stock_code')
        file_name_filter = request.args.get('file_name')

        if stock_code_filter:
            print(f"🎯 只搜索股票代码: {stock_code_filter}")
        if file_name_filter:
            print(f"📄 只搜索文件: {file_name_filter}")

        # 从数据库获取分析结果
        analysis_results = db_manager.get_keyword_analysis(analysis_id)
        print(f"📊 找到 {len(analysis_results)} 条分析结果")

        contexts = []
        for result in analysis_results:
            # 应用过滤条件
            if result['keyword'] == keyword and result['count'] > 0:
                # 检查股票代码过滤
                if stock_code_filter and result.get('stock_code') != stock_code_filter:
                    continue

                # 检查文件名过滤
                if file_name_filter and result.get('file_name') != file_name_filter:
                    continue
                print(f"  🎯 匹配关键词: {result['stock_code']} - {result.get('file_name', 'Unknown')} - {keyword} ({result['count']}次)")

                # 直接通过report_id获取年报内容
                report_id = result.get('report_id')
                if report_id:
                    print(f"  📄 查找年报ID: {report_id}")
                    report = db_manager.get_report_by_id(report_id)

                    if report and report.get('txt_content'):
                        print(f"    ✅ 找到年报，文本长度: {len(report['txt_content'])}")

                        # 验证关键词确实存在
                        import re
                        clean_content = re.sub(r'[^\u4e00-\u9fa5]', '', report['txt_content'])
                        actual_count = clean_content.count(keyword)
                        print(f"    🔍 验证关键词'{keyword}'在清理后文本中的实际出现次数: {actual_count}")

                        # 提取关键词上下文（使用用户指定的长度）
                        context_snippets = extract_keyword_context(report['txt_content'], keyword, context_length)
                        if context_snippets:
                            contexts.append({
                                'stock_code': result['stock_code'],
                                'company_name': result.get('company_name', ''),
                                'file_name': result.get('file_name', ''),
                                'snippets': context_snippets,  # 返回所有片段
                                'total_count': len(context_snippets),
                                'keyword_count': result['count']
                            })
                            print(f"    📝 添加了 {len(context_snippets)} 个上下文片段")
                        else:
                            print(f"    ⚠️ 关键词'{keyword}'上下文提取失败")
                            # 显示文本开头用于调试
                            text_preview = report['txt_content'][:200].replace('\n', ' ')
                            print(f"    📄 文本开头预览: {text_preview}...")
                    elif report:
                        print(f"    ⚠️ 年报没有文本内容")
                    else:
                        print(f"    ❌ 未找到ID为{report_id}的年报")
                else:
                    print(f"    ❌ 分析结果中没有report_id")

        # 计算总片段数
        total_snippets = sum(len(ctx.get('snippets', [])) for ctx in contexts)

        if stock_code_filter or file_name_filter:
            filter_info = []
            if stock_code_filter:
                filter_info.append(f"股票:{stock_code_filter}")
            if file_name_filter:
                filter_info.append(f"文件:{file_name_filter}")
            print(f"🎉 在过滤条件({', '.join(filter_info)})下找到 {len(contexts)} 个文件，共 {total_snippets} 个上下文片段")
        else:
            print(f"🎉 总共找到 {len(contexts)} 个文件，共 {total_snippets} 个上下文片段")

        return jsonify({
            'success': True,
            'keyword': keyword,
            'contexts': contexts
        })

    except Exception as e:
        print(f"❌ 获取上下文失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'获取上下文失败: {str(e)}'
        })


def extract_keyword_context(text, keyword, context_length=200):
    """提取关键词上下文片段"""
    import re

    print(f"    🔧 extract_keyword_context调用: keyword='{keyword}', context_length={context_length}")

    # 使用与关键词统计相同的文本清理方式
    clean_text_for_search = re.sub(r'[^\u4e00-\u9fa5]', '', text)

    # 找到所有关键词位置（在清理后的文本中）
    keyword_positions = []
    start = 0
    while True:
        pos = clean_text_for_search.find(keyword, start)
        if pos == -1:
            break
        keyword_positions.append(pos)
        start = pos + 1

    print(f"    🔍 在清理后的文本中找到 {len(keyword_positions)} 个'{keyword}'位置")

    if not keyword_positions:
        return []

    # 为了显示上下文，我们需要在原始文本中找到对应位置
    # 创建一个映射，从清理后的位置映射到原始文本位置
    original_text = text
    clean_to_original_map = []

    for i, char in enumerate(original_text):
        if re.match(r'[\u4e00-\u9fa5]', char):  # 如果是中文字符
            clean_to_original_map.append(i)

    # 提取上下文片段（提取所有位置）
    snippets = []
    for clean_pos in keyword_positions:  # 提取所有位置
        if clean_pos < len(clean_to_original_map) and clean_pos + len(keyword) <= len(clean_to_original_map):
            # 找到原始文本中关键词的开始和结束位置
            keyword_start = clean_to_original_map[clean_pos]
            keyword_end = clean_to_original_map[clean_pos + len(keyword) - 1] + 1

            # 在原始文本中提取上下文
            start_pos = max(0, keyword_start - context_length)
            end_pos = min(len(original_text), keyword_end + context_length)

            snippet = original_text[start_pos:end_pos]

            # 清理snippet中的多余空白
            snippet = re.sub(r'\s+', ' ', snippet).strip()

            # 智能高亮关键词（处理中间有符号的情况）
            highlighted_snippet = highlight_keyword_in_text(snippet, keyword)

            # 添加省略号
            if start_pos > 0:
                highlighted_snippet = '...' + highlighted_snippet
            if end_pos < len(original_text):
                highlighted_snippet = highlighted_snippet + '...'

            snippets.append(highlighted_snippet)

    print(f"    📝 成功提取 {len(snippets)} 个上下文片段")
    return snippets


def highlight_keyword_in_text(text, keyword):
    """在文本中智能高亮关键词，处理中间有符号的情况"""
    import re

    # 如果关键词长度小于2，使用简单替换
    if len(keyword) < 2:
        return text.replace(keyword, f'<mark class="bg-warning">{keyword}</mark>')

    # 先尝试简单匹配（完全相同）
    if keyword in text:
        simple_highlighted = text.replace(keyword, f'<mark class="bg-warning">{keyword}</mark>')
        print(f"    ✅ 使用简单匹配高亮关键词'{keyword}'")
        return simple_highlighted

    # 如果简单匹配失败，使用智能匹配
    print(f"    🔍 关键词'{keyword}'未找到完全匹配，尝试智能匹配...")

    # 创建关键词的正则表达式模式
    # 在每个字符之间允许插入少量非中文字符
    keyword_chars = list(keyword)
    pattern_parts = []

    for i, char in enumerate(keyword_chars):
        # 转义特殊正则字符
        escaped_char = re.escape(char)
        pattern_parts.append(escaped_char)

        # 在字符之间（除了最后一个字符）添加可选的非中文字符匹配
        # 限制插入字符的数量，避免匹配过于宽泛
        if i < len(keyword_chars) - 1:
            pattern_parts.append(r'[^\u4e00-\u9fa5]{0,3}?')  # 最多3个非中文字符，非贪婪匹配

    pattern = ''.join(pattern_parts)

    print(f"    🎯 关键词'{keyword}'的智能匹配模式: {pattern}")

    def replace_match(match):
        matched_text = match.group(0)
        print(f"    🎯 匹配到文本片段: '{matched_text}'")
        return f'<mark class="bg-warning">{matched_text}</mark>'

    try:
        # 使用正则表达式进行替换
        highlighted_text = re.sub(pattern, replace_match, text)

        # 检查是否有高亮
        if '<mark class="bg-warning">' in highlighted_text:
            print(f"    ✅ 成功智能高亮关键词'{keyword}'")
            return highlighted_text
        else:
            print(f"    ⚠️ 智能匹配也未找到关键词'{keyword}'")
            return text

    except Exception as e:
        print(f"    ❌ 智能高亮失败: {e}")
        return text


def test_highlight_function():
    """测试高亮功能"""
    test_cases = [
        ("人工智能技术发展", "人工智能", "人工智能"),
        ("人工 智能技术发展", "人工智能", "人工 智能"),
        ("人工、智能技术发展", "人工智能", "人工、智能"),
        ("人工（智能）技术发展", "人工智能", "人工（智能）"),
        ("产学研合作", "产学研", "产学研"),
        ("产、学、研合作", "产学研", "产、学、研"),
    ]

    print("🧪 测试关键词高亮功能:")
    for text, keyword, expected in test_cases:
        result = highlight_keyword_in_text(text, keyword)
        print(f"  文本: {text}")
        print(f"  关键词: {keyword}")
        print(f"  结果: {result}")
        print(f"  预期包含: {expected}")
        print("  ---")


@app.route('/api/clean_duplicates', methods=['POST'])
def clean_duplicates():
    """清理重复数据"""
    try:
        # 清理重复的年报记录
        reports_cleaned = db_manager.clean_duplicate_reports()

        # 清理重复的关键词分析记录
        analysis_cleaned = db_manager.clean_duplicate_keyword_analysis()

        return jsonify({
            'success': True,
            'message': f'清理完成：删除了 {reports_cleaned} 条重复年报记录，{analysis_cleaned} 条重复分析记录',
            'reports_cleaned': reports_cleaned,
            'analysis_cleaned': analysis_cleaned
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清理失败: {str(e)}'
        })


@app.route('/api/test_highlight', methods=['POST'])
def test_highlight():
    """测试关键词高亮功能"""
    try:
        data = request.json
        text = data.get('text', '')
        keyword = data.get('keyword', '')

        if not text or not keyword:
            return jsonify({
                'success': False,
                'message': '请提供文本和关键词'
            })

        highlighted_text = highlight_keyword_in_text(text, keyword)

        return jsonify({
            'success': True,
            'original_text': text,
            'keyword': keyword,
            'highlighted_text': highlighted_text
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        })


if __name__ == '__main__':
    # 确保必要的目录存在
    os.makedirs('results/pdf', exist_ok=True)
    os.makedirs('results/txt', exist_ok=True)
    os.makedirs('exports', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
