#!/usr/bin/env python3
"""
测试Unicode修复的脚本
"""
import json

def test_unicode_encoding():
    """测试Unicode编码"""
    test_data = {
        'type': 'contexts',
        'data': [
            {
                'stock_code': '300385',
                'company_name': '雪浪环境',
                'related_party': '无锡市康威输送机械有限公司',
                'context': '这是一个包含中文的测试上下文内容',
                'keywords_found': [],
                'has_keywords': False,
                'context_type': 'related_party_only'
            }
        ]
    }
    
    print("测试Unicode编码...")
    
    # 测试默认编码（可能有问题）
    try:
        json_str_default = json.dumps(test_data)
        print(f"✅ 默认编码成功: {len(json_str_default)} 字符")
        print(f"   预览: {json_str_default[:100]}...")
    except Exception as e:
        print(f"❌ 默认编码失败: {e}")
    
    # 测试ensure_ascii=False编码（修复后）
    try:
        json_str_fixed = json.dumps(test_data, ensure_ascii=False)
        print(f"✅ 修复编码成功: {len(json_str_fixed)} 字符")
        print(f"   预览: {json_str_fixed[:100]}...")
        
        # 测试解析
        parsed_data = json.loads(json_str_fixed)
        print(f"✅ 解析成功: {parsed_data['data'][0]['company_name']}")
        
    except Exception as e:
        print(f"❌ 修复编码失败: {e}")

def test_streaming_format():
    """测试流式格式"""
    test_data = {
        'type': 'ai_chunk',
        'data': '这是一个包含中文的AI响应内容，用于测试流式传输。'
    }
    
    print("\n测试流式格式...")
    
    try:
        # 模拟流式输出格式
        stream_line = f"data: {json.dumps(test_data, ensure_ascii=False)}\n\n"
        print(f"✅ 流式格式生成成功: {len(stream_line)} 字符")
        print(f"   内容: {stream_line}")
        
        # 模拟前端解析
        if stream_line.startswith('data: '):
            json_str = stream_line[6:].strip()
            parsed_data = json.loads(json_str)
            print(f"✅ 前端解析成功: {parsed_data['data']}")
            
    except Exception as e:
        print(f"❌ 流式格式测试失败: {e}")

if __name__ == '__main__':
    test_unicode_encoding()
    test_streaming_format()
    print("\n🎉 Unicode修复测试完成！")
