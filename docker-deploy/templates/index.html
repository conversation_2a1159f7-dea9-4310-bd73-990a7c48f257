<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>巨潮资讯网年报爬虫工具 - 网页版</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        /* 协同创新详情样式 */
        .innovation-details .context-item {
            transition: all 0.3s ease;
        }
        .innovation-details .context-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .innovation-details .context-content {
            line-height: 1.6;
            font-size: 0.95em;
        }
        .innovation-details .text-content {
            max-height: 200px;
            overflow-y: auto;
        }
        .innovation-details .badge {
            margin: 2px;
        }

        /* AI流式响应样式 */
        #aiStreamingContent {
            min-height: 100px;
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.6;
        }

        .streaming-cursor {
            display: inline-block;
            width: 2px;
            height: 1.2em;
            background-color: #007bff;
            animation: blink 1s infinite;
            margin-left: 2px;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .context-item {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin-bottom: 15px;
        }

        .context-meta {
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 8px;
        }

        .context-text {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-size: 0.95em;
            line-height: 1.5;
        }

        .keyword-highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        /* 折叠按钮动画 */
        #contextToggleIcon {
            transition: transform 0.3s ease;
        }

        .collapsed #contextToggleIcon {
            transform: rotate(0deg);
        }

        .not-collapsed #contextToggleIcon {
            transform: rotate(90deg);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 头部 -->
        <div class="row">
            <div class="col-12">
                <div class="header-section">
                    <h1 class="text-center mb-4">
                        <i class="bi bi-building"></i> 巨潮资讯网年报爬虫工具
                        <small class="text-muted">网页版</small>
                    </h1>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 左侧控制面板 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> 控制面板</h5>
                    </div>
                    <div class="card-body">
                        <!-- 数据源选择 -->
                        <div class="mb-3">
                            <label class="form-label"><i class="bi bi-database"></i> 数据源选择</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="dataSource" id="onlineSource" value="online" checked>
                                <label class="form-check-label" for="onlineSource">
                                    在线下载PDF
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="dataSource" id="localSource" value="local">
                                <label class="form-check-label" for="localSource">
                                    使用本地数据库
                                </label>
                            </div>
                        </div>

                        <!-- 股票代码输入 -->
                        <div class="mb-3">
                            <label for="stockCodes" class="form-label">
                                <i class="bi bi-graph-up"></i> 股票代码 (每行一个)
                            </label>
                            <textarea class="form-control" id="stockCodes" rows="4" placeholder="300454&#10;300504&#10;300514">300454
300504
300514</textarea>
                        </div>

                        <!-- 搜索关键字 -->
                        <div class="mb-3" id="searchKeywordGroup">
                            <label for="searchKeyword" class="form-label">
                                <i class="bi bi-search"></i> 搜索关键字
                            </label>
                            <input type="text" class="form-control" id="searchKeyword" value="年度报告" placeholder="年度报告">
                        </div>

                        <!-- 在线下载时间范围 -->
                        <div class="row mb-3" id="dateRangeGroup">
                            <div class="col-12 mb-2">
                                <label class="form-label">
                                    <i class="bi bi-calendar-range"></i> 公告发布时间范围
                                </label>
                            </div>
                            <div class="col-6">
                                <label for="startDate" class="form-label small">开始日期</label>
                                <input type="date" class="form-control form-control-sm" id="startDate" value="2025-01-01">
                            </div>
                            <div class="col-6">
                                <label for="endDate" class="form-label small">结束日期</label>
                                <input type="date" class="form-control form-control-sm" id="endDate" value="2025-12-31">
                            </div>
                        </div>



                        <!-- 统计关键词 -->
                        <div class="mb-3">
                            <label for="keywords" class="form-label">
                                <i class="bi bi-tags"></i> 统计关键词 (每行一个)
                            </label>
                            <textarea class="form-control" id="keywords" rows="6" placeholder="请输入创新关键词">协同创新
合资设立
合作开发
合作研发
共同开发
联合研发
战略合作
整合资源
共同研发
个性化需求
客户合作
消费者合作
用户合作
协同开发
客户需求
技术交流
战略合作协议
产学研
联合开发
项目合作
协同创新
合作创新
战略联盟
研发联盟
开放式创新
研究院
技术合作
校企合作
联合申报
研发中心
博士后工作站
专家工作站
技术中心
科技计划立项
国家重点研发计划
技术服务
用户互动
客户需要
客户满意度
贴合顾客需求
围绕客户
消费者需求
合作协议
许可协议
合作项目
合资公司
消费者中心
资源共享
知识共享
知识溢出
信息共享
共享平台
价值共创
用户共创
资源互补
联合推出
联合生产
ODM
合作设立
研究所
共享创新
共同研制
协同效应
战略协作
资源整合
共同探索
客户偏好
研究平台
用户偏好
协同发展
共同布局
战略交流
政产学研
科研合作
融通创新
客户喜好
用户喜好
校企联合
共同申报
研究中心
科技战略联盟
研发机构
客户至上
消费者至上
合营公司
客户中心
研发共享
要素流动
资源充分结合
加强与企业的合作
战略框架协议
联盟
工作站
研究机构
高校机构
科研单位
用户导流
消费者调研
客户反馈
合作框架协议
协作研发
研发协同
创新协作
技术联盟
科研机构
联合实验室
共建实验室
科技园区
共建研发基地
用户需求
顾客需求
顾客至上
以客户为中心
以消费者为中心
合资企业
用户中心
资源协同
知识协同
要素共享
用户平台
要素互通</textarea>
                        </div>

                        <!-- 关联方分析 -->
                        <div class="mb-3">
                            <label for="relatedParties" class="form-label">
                                <i class="bi bi-people"></i> 关联方分析 (每行一个，可选)
                            </label>
                            <textarea class="form-control" id="relatedParties" rows="3" placeholder="华为技术有限公司&#10;腾讯科技&#10;阿里巴巴"></textarea>
                            <small class="text-muted">💡 输入供应商、经销商等关联方名称</small>
                        </div>

                        <!-- 上下文设置 -->
                        <div class="mb-3">
                            <label for="contextLength" class="form-label">
                                <i class="bi bi-text-paragraph"></i> 上下文片段长度
                            </label>
                            <select class="form-select" id="contextLength">
                                <option value="100" selected>简短 (100字符)</option>
                                <option value="200">标准 (200字符)</option>
                                <option value="300">详细 (300字符)</option>
                                <option value="500">完整 (500字符)</option>
                            </select>
                            <small class="text-muted">💡 设置查看关键词上下文时显示的内容长度</small>
                        </div>

                        <!-- 控制按钮 -->
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-primary" id="startAnalysisBtn">
                                <i class="bi bi-play-circle"></i> 开始分析
                            </button>
                            <button type="button" class="btn btn-danger" id="stopAnalysisBtn" disabled>
                                <i class="bi bi-stop-circle"></i> 停止分析
                            </button>
                            <button type="button" class="btn btn-info" id="keywordOnlyBtn">
                                <i class="bi bi-search"></i> 仅关键词分析
                            </button>
                            <button type="button" class="btn btn-secondary" id="importTxtBtn">
                                <i class="bi bi-upload"></i> 导入TXT文件
                            </button>
                            <button type="button" class="btn btn-warning" id="cleanDuplicatesBtn">
                                <i class="bi bi-trash"></i> 清理重复数据
                            </button>
                            <button type="button" class="btn btn-success" id="aiAnalysisBtn">
                                <i class="bi bi-robot"></i> AI分析
                            </button>
                            <button type="button" class="btn btn-outline-info" id="debugDatabaseBtn">
                                <i class="bi bi-bug"></i> 调试数据库
                            </button>
<!--                            <button type="button" class="btn btn-danger" onclick="forceCloseAllModals()" title="强制关闭所有弹窗">-->
<!--                                <i class="bi bi-x-circle"></i> 强制关闭弹窗-->
<!--                            </button>-->
                        </div>
                    </div>
                </div>

                <!-- 进度显示 -->
                <div class="card mt-3" id="progressCard" style="display: none;">
                    <div class="card-header">
                        <h6><i class="bi bi-clock"></i> 任务进度</h6>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-2">
                            <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="progressText" class="text-muted small">准备开始...</div>
                        <div id="taskStatus" class="mt-2">
                            <span class="badge bg-secondary">等待中</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧结果展示区域 -->
            <div class="col-md-8">
                <!-- 日志输出 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-terminal"></i> 日志输出</h5>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="clearLogBtn">
                            <i class="bi bi-trash"></i> 清空
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="logOutput" class="log-output">
                            <div class="log-entry">
                                <span class="timestamp">[系统启动]</span>
                                <span class="message">系统已就绪，请选择操作...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结果展示 -->
                <div class="card mt-3" id="resultsCard" style="display: none;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-table"></i> 分析结果</h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-success" id="exportResultsBtn">
                                <i class="bi bi-download"></i> 导出Excel
                            </button>
                            <button type="button" class="btn btn-sm btn-info" id="showSummaryBtn">
                                <i class="bi bi-bar-chart"></i> 统计摘要
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="resultsContent">
                            <!-- 结果表格将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计摘要模态框 -->
    <div class="modal fade" id="summaryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-bar-chart"></i> 统计摘要</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="summaryContent">
                        <!-- 摘要内容将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI分析模态框 -->
    <div class="modal fade" id="aiAnalysisModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-robot"></i> AI分析
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- AI分析输入区域 -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <label for="aiStockCodes" class="form-label">
                                <i class="bi bi-graph-up"></i> 股票代码
                            </label>
                            <textarea class="form-control" id="aiStockCodes" rows="3" placeholder="000001&#10;000002&#10;600000"></textarea>
                            <small class="text-muted">每行一个股票代码</small>
                        </div>
                        <div class="col-md-4">
                            <label for="aiKeywords" class="form-label">
                                <i class="bi bi-search"></i> 统计关键词
                            </label>
                            <textarea class="form-control" id="aiKeywords" rows="3" placeholder="协同创新&#10;技术合作&#10;研发合作"></textarea>
                            <small class="text-muted">每行一个关键词</small>
                        </div>
                        <div class="col-md-4">
                            <label for="aiRelatedParties" class="form-label">
                                <i class="bi bi-people"></i> 关联方
                            </label>
                            <textarea class="form-control" id="aiRelatedParties" rows="3" placeholder="华为技术有限公司&#10;腾讯科技&#10;阿里巴巴"></textarea>
                            <small class="text-muted">每行一个关联方名称</small>
                        </div>
                    </div>

                    <!-- AI分析提示词 -->
                    <div class="mb-4">
                        <label for="aiPrompt" class="form-label">
                            <i class="bi bi-chat-dots"></i> AI分析要求
                        </label>
                        <textarea class="form-control" id="aiPrompt" rows="3" placeholder="根据搜索到的全部关联方公司的上下文内容，判断股票代码对应的公司和哪些关联方公司存在协同创新。"></textarea>
                        <small class="text-muted">描述您希望AI如何分析这些内容</small>
                    </div>

                    <!-- OpenAI配置区域 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-gear"></i> OpenAI配置 (可选)
                                <button type="button" class="btn btn-sm btn-outline-info ms-2" onclick="toggleOpenAIConfig()">
                                    <i class="bi bi-info-circle"></i> 说明
                                </button>
                                <small class="text-muted ms-2">留空则使用服务器默认配置</small>
                            </h6>
                        </div>
                        <div class="card-body" id="openaiConfigBody" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="openaiApiKey" class="form-label">API Key</label>
                                    <input type="password" class="form-control" id="openaiApiKey" placeholder="sk-...">
                                    <small class="text-muted">您的OpenAI API密钥</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="openaiBaseUrl" class="form-label">Base URL</label>
                                    <input type="text" class="form-control" id="openaiBaseUrl" placeholder="https://api.openai.com/v1" value="https://api.openai.com/v1">
                                    <small class="text-muted">API基础URL（支持new-api等代理）</small>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="openaiModel" class="form-label">模型</label>
                                    <select class="form-select" id="openaiModel">
                                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                        <option value="gpt-4">GPT-4</option>
                                        <option value="gpt-4-turbo">GPT-4 Turbo</option>
                                        <option value="gpt-4o">GPT-4o</option>
                                        <option value="o3">O3</option>
                                    </select>
                                    <small class="text-muted">选择要使用的AI模型</small>
                                </div>
                                <div class="col-md-6 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-success" onclick="testOpenAIConnection()">
                                        <i class="bi bi-check-circle"></i> 测试连接
                                    </button>
                                </div>
                            </div>
                            <div class="alert alert-info mt-3">
                                <i class="bi bi-info-circle"></i>
                                <strong>配置说明：</strong>
                                <ul class="mb-0 mt-2">
                                    <li><strong>留空使用默认：</strong>如果不填写API Key，系统将使用服务器端的.env配置</li>
                                    <li><strong>临时覆盖：</strong>填写API Key可临时覆盖服务器配置</li>
                                    <li><strong>官方OpenAI：</strong>Base URL使用 <code>https://api.openai.com/v1</code></li>
                                    <li><strong>new-api代理：</strong>Base URL使用您的代理地址，如 <code>https://your-domain.com/v1</code></li>
                                    <li><strong>无配置：</strong>如果服务器也未配置，将使用模拟AI分析</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 控制按钮 -->
                    <div class="text-center mb-4">
                        <button type="button" class="btn btn-primary" id="startAiAnalysisBtn">
                            <i class="bi bi-play-circle"></i> 开始AI分析
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" id="clearAiFormBtn">
                            <i class="bi bi-arrow-clockwise"></i> 清空表单
                        </button>
                    </div>

                    <!-- AI分析结果 -->
                    <div id="aiAnalysisResults" style="display: none;">
                        <h6><i class="bi bi-lightbulb"></i> AI分析结果</h6>

                        <!-- AI分析内容 -->
                        <div id="aiAnalysisContent" class="border rounded p-3 bg-light mb-3">
                            <div id="aiStreamingContent">
                                <!-- 流式AI分析结果将在这里显示 -->
                            </div>
                            <div id="aiStreamingIndicator" style="display: none;">
                                <i class="bi bi-three-dots text-primary"></i>
                                <span class="text-muted">AI正在分析中...</span>
                            </div>
                        </div>

                        <!-- 折叠的上下文引用 -->
                        <div class="card">
                            <div class="card-header">
                                <button class="btn btn-link text-decoration-none p-0 w-100 text-start" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#contextReferences"
                                        aria-expanded="false" aria-controls="contextReferences">
                                    <i class="bi bi-chevron-right" id="contextToggleIcon"></i>
                                    <i class="bi bi-quote"></i>
                                    引用上下文 (<span id="contextCount">0</span>)
                                </button>
                            </div>
                            <div class="collapse" id="contextReferences">
                                <div class="card-body" id="contextContent">
                                    <!-- 搜索到的上下文将在这里显示 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 关联方分析分页弹窗 -->
    <div class="modal fade" id="relatedPartyModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-people-fill"></i> 关联方协同创新分析
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 统计信息 -->
                    <div id="relatedPartyStats" class="mb-4">
                        <!-- 统计卡片将在这里显示 -->
                    </div>

                    <!-- 分页控制 -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">详细分析结果</h6>
                        <div class="d-flex align-items-center">
                            <label for="relatedPartyPageSize" class="form-label me-2 mb-0">每页显示:</label>
                            <select class="form-select form-select-sm me-3" id="relatedPartyPageSize" style="width: auto;" onchange="updateRelatedPartyPageSize()">
                                <option value="5">5条</option>
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                            </select>
                            <span id="relatedPartyPageInfo" class="text-muted small"></span>
                        </div>
                    </div>

                    <!-- 关联方分析内容 -->
                    <div id="relatedPartyContent">
                        <!-- 分页内容将在这里显示 -->
                    </div>

                    <!-- 分页导航 -->
                    <div id="relatedPartyPagination" class="d-flex justify-content-center mt-4">
                        <!-- 分页控件将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="mt-2" id="loadingText">处理中...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
