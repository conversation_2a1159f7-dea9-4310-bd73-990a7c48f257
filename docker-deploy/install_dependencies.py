#!/usr/bin/env python3
"""
安装依赖脚本
"""
import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {package} - {e}")
        return False

def main():
    print("🔧 开始安装依赖...")
    
    # 必需的包
    required_packages = [
        "python-dotenv==1.0.0",
        "openai==0.28.1",
        "Flask==2.3.3",
        "requests==2.31.0"
    ]
    
    success_count = 0
    for package in required_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 安装结果: {success_count}/{len(required_packages)} 个包安装成功")
    
    if success_count == len(required_packages):
        print("✅ 所有依赖安装成功！")
        
        # 测试导入
        print("\n🔧 测试导入...")
        try:
            from dotenv import load_dotenv
            print("✅ python-dotenv 导入成功")
        except ImportError:
            print("❌ python-dotenv 导入失败")
        
        try:
            import openai
            print("✅ openai 导入成功")
        except ImportError:
            print("❌ openai 导入失败")
            
        # 检查.env文件
        print("\n🔧 检查.env文件...")
        if os.path.exists('.env'):
            print("✅ .env文件存在")
            load_dotenv()
            api_key = os.getenv('OPENAI_API_KEY', '')
            if api_key:
                print(f"✅ OPENAI_API_KEY已配置 (长度: {len(api_key)})")
            else:
                print("⚠️ OPENAI_API_KEY未配置")
        else:
            print("⚠️ .env文件不存在，请创建并配置")
    else:
        print("❌ 部分依赖安装失败，请检查网络连接或权限")

if __name__ == "__main__":
    main()
