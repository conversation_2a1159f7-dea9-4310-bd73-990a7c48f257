#!/usr/bin/env python3
"""
部署检查脚本
用于诊断服务器部署问题
"""
import requests
import json
import time

def test_basic_api():
    """测试基础API"""
    try:
        response = requests.get('http://localhost:5000/')
        if response.status_code == 200:
            print("✅ 基础页面访问正常")
            return True
        else:
            print(f"❌ 基础页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 基础页面访问异常: {e}")
        return False

def test_database_api():
    """测试数据库API"""
    try:
        response = requests.get('http://localhost:5000/api/debug_database')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 数据库API正常")
                print(f"   数据库状态: {data.get('debug_info', {})}")
                return True
            else:
                print(f"❌ 数据库API失败: {data.get('message')}")
                return False
        else:
            print(f"❌ 数据库API HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 数据库API异常: {e}")
        return False

def test_streaming_api():
    """测试流式API"""
    try:
        test_data = {
            "stock_codes": "000001",
            "keywords": "技术",
            "related_parties": "腾讯",
            "prompt": "测试分析"
        }
        
        print("🔄 测试流式API...")
        response = requests.post(
            'http://localhost:5000/api/ai_analysis_stream',
            json=test_data,
            stream=True,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 流式API连接成功")
            
            # 读取流式响应
            chunk_count = 0
            for line in response.iter_lines():
                if line:
                    chunk_count += 1
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        try:
                            data = json.loads(line_str[6:])
                            print(f"   接收块 {chunk_count}: {data.get('type', 'unknown')}")
                            if chunk_count >= 5:  # 只测试前5个块
                                break
                        except json.JSONDecodeError:
                            print(f"   接收块 {chunk_count}: 非JSON数据")
            
            if chunk_count > 0:
                print(f"✅ 流式响应正常，接收到 {chunk_count} 个数据块")
                return True
            else:
                print("❌ 流式响应无数据")
                return False
        else:
            print(f"❌ 流式API HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 流式API异常: {e}")
        return False

def test_openai_config():
    """测试OpenAI配置"""
    try:
        test_data = {
            "use_default": True
        }
        
        response = requests.post(
            'http://localhost:5000/api/test_openai',
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ OpenAI配置测试成功")
                return True
            else:
                print(f"⚠️ OpenAI配置测试失败: {data.get('message')}")
                return False
        else:
            print(f"❌ OpenAI测试API HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ OpenAI测试API异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始部署检查...")
    print("=" * 50)
    
    tests = [
        ("基础页面", test_basic_api),
        ("数据库API", test_database_api),
        ("流式API", test_streaming_api),
        ("OpenAI配置", test_openai_config)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
        time.sleep(1)  # 间隔1秒
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过，部署正常！")
    else:
        print("\n⚠️ 部分测试失败，请检查服务器配置")

if __name__ == '__main__':
    main()
