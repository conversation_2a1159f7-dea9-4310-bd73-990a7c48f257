# ========================================
# OpenAI配置（可选）
# ========================================
# 如果配置了这些参数，AI分析功能将使用真实的OpenAI API
# 如果不配置，系统将使用模拟AI响应

# OpenAI API密钥（必填）
OPENAI_API_KEY=sk-your-api-key-here

# API基础URL（可选，默认为官方API）
OPENAI_BASE_URL=https://api.openai.com/v1

# 使用的模型（可选，默认为gpt-3.5-turbo）
OPENAI_MODEL=gpt-3.5-turbo

# ========================================
# 支持的模型列表：
# ========================================
# - gpt-3.5-turbo (推荐，性价比高)
# - gpt-4 (更强大但成本较高)
# - gpt-4-turbo (GPT-4的优化版本)
# - gpt-4o (多模态模型)
# - o3 (最新的推理模型)

# ========================================
# new-api代理配置示例：
# ========================================
# 如果您使用new-api等代理服务，请修改BASE_URL
# OPENAI_BASE_URL=https://your-new-api-domain.com/v1
# OPENAI_API_KEY=sk-your-new-api-key
# OPENAI_MODEL=gpt-4o

# ========================================
# 使用说明：
# ========================================
# 1. 复制此文件为 .env
# 2. 填写您的API配置
# 3. 重启应用
# 4. 前端可以留空OpenAI配置，系统将自动使用这里的默认配置
# 5. 前端也可以临时填写配置来覆盖默认设置
