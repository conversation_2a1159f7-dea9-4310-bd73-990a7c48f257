#!/usr/bin/env python3
"""
服务器启动脚本
解决部署环境的兼容性问题
"""
import os
import sys
from app import app

def check_environment():
    """检查环境配置"""
    print("🔧 检查服务器环境...")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查依赖
    try:
        import flask
        print(f"✅ Flask版本: {flask.__version__}")
    except ImportError:
        print("❌ Flask未安装")
        return False
    
    try:
        import openai
        print(f"✅ OpenAI版本: {openai.__version__}")
    except ImportError:
        print("⚠️ OpenAI未安装，将使用模拟响应")
    
    # 检查.env文件
    if os.path.exists('.env'):
        print("✅ .env文件存在")
    else:
        print("⚠️ .env文件不存在")
    
    # 检查数据库目录
    db_dir = 'data/database'
    if not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
        print(f"✅ 创建数据库目录: {db_dir}")
    
    # 检查静态文件
    static_files = [
        'static/js/app.js',
        'static/css/style.css',
        'templates/index.html'
    ]
    
    for file_path in static_files:
        if os.path.exists(file_path):
            print(f"✅ 静态文件存在: {file_path}")
        else:
            print(f"❌ 静态文件缺失: {file_path}")
            return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动服务器...")
    
    if not check_environment():
        print("❌ 环境检查失败，请修复后重试")
        sys.exit(1)
    
    # 设置服务器配置
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    print(f"🌐 服务器配置: {host}:{port}, Debug: {debug}")
    
    # 启动应用
    app.run(
        host=host,
        port=port,
        debug=debug,
        threaded=True  # 支持多线程
    )

if __name__ == '__main__':
    main()
