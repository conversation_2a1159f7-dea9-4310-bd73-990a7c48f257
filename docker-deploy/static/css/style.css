/* 自定义样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
}

.header-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-section h1 {
    margin: 0;
    font-weight: 300;
}

.header-section small {
    opacity: 0.8;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.card-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: none;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 500;
}

.card-header h5, .card-header h6 {
    margin: 0;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ee82f0 0%, #f3455a 100%);
    transform: translateY(-1px);
}

.btn-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
}

.btn-info:hover {
    background: linear-gradient(135deg, #3d9bfe 0%, #00e0fe 100%);
    transform: translateY(-1px);
}

.btn-secondary {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border: none;
    color: #495057;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #96e6e2 0%, #fcc4d1 100%);
    transform: translateY(-1px);
    color: #495057;
}

.btn-success {
    background: linear-gradient(135deg, #81FBB8 0%, #28C76F 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #6ff9a6 0%, #24b865 100%);
    transform: translateY(-1px);
}

/* 日志输出样式 */
.log-output {
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    height: 300px;
    overflow-y: auto;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #333;
}

.log-entry {
    margin-bottom: 5px;
    line-height: 1.4;
}

.log-entry .timestamp {
    color: #569cd6;
    font-weight: 500;
}

.log-entry .message {
    color: #d4d4d4;
}

.log-entry.success .message {
    color: #4ec9b0;
}

.log-entry.error .message {
    color: #f44747;
}

.log-entry.warning .message {
    color: #ffcc02;
}

.log-entry.info .message {
    color: #9cdcfe;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* 表格样式 */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 500;
    padding: 12px;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody td {
    padding: 10px 12px;
    border-color: #dee2e6;
}

/* 徽章样式 */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 6px;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important;
    color: #495057;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #81FBB8 0%, #28C76F 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%) !important;
    color: #495057;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px 15px 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-section {
        padding: 1rem 0;
        margin-bottom: 1rem;
    }
    
    .header-section h1 {
        font-size: 1.5rem;
    }
    
    .log-output {
        height: 200px;
        font-size: 12px;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 滚动条样式 */
.log-output::-webkit-scrollbar {
    width: 8px;
}

.log-output::-webkit-scrollbar-track {
    background: #2d2d2d;
    border-radius: 4px;
}

.log-output::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.log-output::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* 表单控件增强 */
.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 结果表格特殊样式 */
.results-table {
    font-size: 0.9rem;
}

.results-table .keyword-cell {
    font-weight: 600;
    color: #fff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    text-align: center;
    white-space: nowrap;
    min-width: 80px;
}

.results-table .count-cell {
    text-align: center;
    font-weight: 600;
    vertical-align: middle;
}

.results-table .count-zero {
    color: #6c757d;
    opacity: 0.7;
}

.results-table .count-high {
    color: #28a745;
    font-weight: 700;
}

.results-table .btn-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
}

.results-table .d-flex {
    gap: 0.25rem;
}

/* 统计卡片 */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.stat-card h3 {
    font-size: 2rem;
    font-weight: 300;
    margin: 0;
}

.stat-card p {
    margin: 0;
    opacity: 0.9;
}

/* AI分析上下文样式 */
.context-section {
    margin-bottom: 2rem;
}

.context-item {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    background-color: #fff;
    transition: all 0.3s ease;
}

.context-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.context-item.border-success {
    border-color: #28a745;
    border-left: 5px solid #28a745;
}

.context-item.border-info {
    border-color: #17a2b8;
    border-left: 5px solid #17a2b8;
}

.context-meta {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
    font-size: 0.9rem;
}

.context-text {
    line-height: 1.6;
    color: #495057;
    font-size: 0.95rem;
}

.keyword-highlight {
    background-color: #fff3cd;
    color: #856404;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
    border: 1px solid #ffeaa7;
}

/* 分页控件样式优化 */
.pagination-sm .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 4px;
}

.pagination-sm .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

.pagination-sm .page-link:hover {
    background-color: #f8f9fa;
    border-color: #667eea;
    color: #667eea;
}

/* 按钮组样式 */
.btn-group-sm .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

.btn-outline-success.active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-color: #28a745;
    color: white;
}

.btn-outline-info.active {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    border-color: #17a2b8;
    color: white;
}

/* AI分析界面左右布局样式 */
#aiInputPanel .card,
#aiResultsPanel .card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.ai-content-display {
    line-height: 1.6;
    font-size: 0.95rem;
    min-height: 200px;
}

/* AI分析占位符样式 */
#aiAnalysisPlaceholder {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-radius: 8px;
    margin: 0 !important;
    height: 100% !important;
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1 !important;
}

#aiAnalysisPlaceholder .text-center {
    padding: 3rem 2rem;
    max-width: 400px;
}

#aiAnalysisPlaceholder i {
    color: #6c757d;
    margin-bottom: 1.5rem;
    display: block;
}

#aiAnalysisPlaceholder p {
    color: #6c757d;
    font-size: 1.1rem;
    font-weight: 500;
    margin: 0;
    line-height: 1.5;
}

/* 确保AI分析结果容器正确隐藏 */
#aiAnalysisResults {
    height: 100% !important;
    width: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 2 !important;
    background: white;
}

/* 确保卡片主体有正确的高度和定位 */
#aiResultsPanel .card-body {
    position: relative !important;
    overflow: hidden !important;
    height: 700px !important;
}

/* 上下文折叠样式 */
#contextReferences .collapse {
    transition: all 0.3s ease;
}

#contextToggleIcon {
    transition: transform 0.3s ease;
}

/* 折叠状态的图标旋转 */
[aria-expanded="true"] #contextToggleIcon {
    transform: rotate(90deg);
}

/* AI分析历史记录样式 */
.history-item {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-item:hover {
    background-color: #f8f9fa;
}

.history-item.active {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.history-item:last-child {
    border-bottom: none;
}

.history-meta {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.history-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.history-summary {
    font-size: 0.8rem;
    color: #6c757d;
    line-height: 1.4;
}

.history-tags {
    margin-top: 0.5rem;
}

.history-tag {
    display: inline-block;
    background-color: #e9ecef;
    color: #495057;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

/* 历史记录详情样式 */
#historyDetail .analysis-content {
    line-height: 1.6;
    font-size: 0.95rem;
}

#historyDetail .analysis-meta {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #007bff;
}

#historyDetail .analysis-meta h6 {
    color: #007bff;
    margin-bottom: 0.5rem;
}

#historyDetail .analysis-meta .meta-item {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

#historyDetail .analysis-meta .meta-label {
    font-weight: 600;
    color: #495057;
}

/* 历史记录空状态样式 */
.history-empty {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.history-empty i {
    font-size: 3rem;
    opacity: 0.3;
    margin-bottom: 1rem;
    display: block;
}

/* 上下文内容样式 */
#contextContent {
    max-height: 400px;
    overflow-y: auto;
}

/* Markdown样式 */
.markdown-content {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #2c3e50;
}

.markdown-content h1 {
    font-size: 1.5rem;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.markdown-content h2 {
    font-size: 1.3rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.3rem;
}

.markdown-content h3 {
    font-size: 1.1rem;
}

.markdown-content p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.markdown-content ul,
.markdown-content ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.markdown-content li {
    margin-bottom: 0.25rem;
}

.markdown-content blockquote {
    border-left: 4px solid #667eea;
    background-color: #f8f9fa;
    padding: 0.75rem 1rem;
    margin: 1rem 0;
    font-style: italic;
}

.markdown-content code {
    background-color: #f1f3f4;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #d63384;
}

.markdown-content pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
    overflow-x: auto;
    margin: 1rem 0;
}

.markdown-content pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
}

.markdown-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.markdown-content th,
.markdown-content td {
    border: 1px solid #e9ecef;
    padding: 0.5rem;
    text-align: left;
}

.markdown-content th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.markdown-content strong {
    font-weight: 600;
    color: #2c3e50;
}

.markdown-content em {
    font-style: italic;
    color: #6c757d;
}

/* 上下布局样式 */
.vertical-layout #aiAnalysisLeftPanel,
.vertical-layout #aiAnalysisRightPanel {
    width: 100%;
    max-width: none;
}

.vertical-layout #aiAnalysisRightPanel {
    margin-top: 1rem;
}

/* 响应式调整 */
@media (max-width: 991.98px) {
    #aiAnalysisContainer {
        flex-direction: column;
    }

    #aiAnalysisRightPanel {
        margin-top: 1rem;
    }

    #aiAnalysisContent,
    #contextContent {
        max-height: 400px;
    }
}
